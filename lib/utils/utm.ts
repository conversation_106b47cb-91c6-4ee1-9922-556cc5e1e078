/**
 * Utility functions for handling UTM parameters
 */

/**
 * Adds UTM parameters to a URL for tracking purposes
 * @param url - The original URL
 * @param utmParams - Object containing UTM parameters
 * @returns URL with UTM parameters appended
 */
export function addUTMParameters(url: string, utmParams: {
  source?: string
  medium?: string
  campaign?: string
  term?: string
  content?: string
}): string {
  if (!url) return url
  
  try {
    const urlObj = new URL(url)
    
    // Add UTM parameters
    if (utmParams.source) urlObj.searchParams.set('utm_source', utmParams.source)
    if (utmParams.medium) urlObj.searchParams.set('utm_medium', utmParams.medium)
    if (utmParams.campaign) urlObj.searchParams.set('utm_campaign', utmParams.campaign)
    if (utmParams.term) urlObj.searchParams.set('utm_term', utmParams.term)
    if (utmParams.content) urlObj.searchParams.set('utm_content', utmParams.content)
    
    return urlObj.toString()
  } catch (error) {
    // If URL is invalid, return original URL
    console.warn('Invalid URL provided to addUTMParameters:', url)
    return url
  }
}

/**
 * Adds default directory UTM parameters for business website links
 * @param url - The business website URL
 * @returns URL with directory UTM parameters
 */
export function addDirectoryUTM(url: string): string {
  return addUTMParameters(url, {
    source: 'findcaraccidentlawyers',
    medium: 'directory',
    campaign: 'listing'
  })
}
