import { Attorney } from '../supabase'

export function generateA<PERSON>rney<PERSON>lug(attorney: Attorney): string {
  return attorney.title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

export function generateCitySlug(city: string): string {
  return city
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

export function generateStateSlug(state: string): string {
  return state
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

export function generateMetaTitle(attorney: Attorney): string {
  return `${attorney.title} - Car Accident Attorney | ${attorney.city}, ${attorney.state} (${attorney.total_score}★)`
}

export function generateMetaDescription(attorney: Attorney): string {
  const snippet = attorney.seo_snippets?.[0]?.text
  if (snippet) {
    return snippet.substring(0, 157) + '...'
  }
  
  return `Contact ${attorney.title} in ${attorney.city}, ${attorney.state}. Rated ${attorney.total_score} stars with ${attorney.reviews_count} reviews. Experienced car accident attorney.`
}

export function generateCityMetaTitle(city: string, state: string, count?: number): string {
  const countText = count ? ` - ${count} Attorneys` : ''
  return `Best Car Accident Attorneys in ${city}, ${state}${countText} | Find Top Lawyers`
}

export function generateCityMetaDescription(city: string, state: string, count?: number): string {
  const countText = count ? ` Browse ${count} verified attorneys` : ''
  return `Find the best car accident attorneys in ${city}, ${state}.${countText} with reviews, ratings, and contact information. Get expert legal help for your car accident case.`
}

export function generateStateMetaTitle(state: string, count?: number): string {
  const countText = count ? ` - ${count} Attorneys` : ''
  return `Car Accident Attorneys in ${state}${countText} | Top-Rated Lawyers Directory`
}

export function generateStateMetaDescription(state: string, count?: number): string {
  const countText = count ? ` Browse ${count} verified attorneys` : ''
  return `Find experienced car accident attorneys throughout ${state}.${countText} with verified reviews and ratings. Connect with top personal injury lawyers in your area.`
}

export function generateBreadcrumbs(attorney?: Attorney, city?: string, state?: string) {
  const breadcrumbs = [
    { name: 'Home', href: '/' }
  ]

  if (state) {
    breadcrumbs.push({
      name: state,
      href: `/${generateStateSlug(state)}`
    })
  }

  if (city && state) {
    breadcrumbs.push({
      name: city,
      href: `/${generateStateSlug(state)}/${generateCitySlug(city)}`
    })
  }

  if (attorney) {
    breadcrumbs.push({
      name: attorney.title,
      href: `/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${generateAttorneySlug(attorney)}`
    })
  }

  return breadcrumbs
}

export function generateStructuredData(attorney: Attorney) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://findcaraccidentattorneys.org'
  
  return {
    '@context': 'https://schema.org',
    '@graph': [
      {
        '@type': 'Attorney',
        '@id': `${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorney.source_id}#attorney`,
        name: attorney.title,
        url: `${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorney.source_id}`,
        telephone: attorney.phone,
        address: {
          '@type': 'PostalAddress',
          streetAddress: attorney.street || attorney.address,
          addressLocality: attorney.city,
          addressRegion: attorney.state,
          postalCode: attorney.postal_code,
          addressCountry: 'US'
        },
        geo: {
          '@type': 'GeoCoordinates',
          latitude: attorney.lat,
          longitude: attorney.lng
        },
        aggregateRating: attorney.reviews_count > 0 ? {
          '@type': 'AggregateRating',
          ratingValue: attorney.total_score,
          reviewCount: attorney.reviews_count,
          bestRating: 5,
          worstRating: 1
        } : undefined,
        areaServed: {
          '@type': 'City',
          name: attorney.city,
          containedInPlace: {
            '@type': 'State',
            name: attorney.state
          }
        },
        knowsAbout: attorney.service_keywords || ['Car Accident Law', 'Personal Injury', 'Auto Accident Claims']
      },
      {
        '@type': 'LocalBusiness',
        '@id': `${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorney.source_id}#business`,
        name: attorney.title,
        url: attorney.website || `${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorney.source_id}`,
        telephone: attorney.phone,
        address: {
          '@type': 'PostalAddress',
          streetAddress: attorney.street || attorney.address,
          addressLocality: attorney.city,
          addressRegion: attorney.state,
          postalCode: attorney.postal_code,
          addressCountry: 'US'
        },
        geo: {
          '@type': 'GeoCoordinates',
          latitude: attorney.lat,
          longitude: attorney.lng
        },
        aggregateRating: attorney.reviews_count > 0 ? {
          '@type': 'AggregateRating',
          ratingValue: attorney.total_score,
          reviewCount: attorney.reviews_count,
          bestRating: 5,
          worstRating: 1
        } : undefined,
        openingHours: attorney.opening_hours ? (
          Array.isArray(attorney.opening_hours)
            ? attorney.opening_hours.map((schedule: any) => `${schedule.day} ${schedule.hours}`)
            : Object.entries(attorney.opening_hours).map(([day, hours]) => `${day} ${hours}`)
        ) : undefined
      },
      ...(attorney.faq_items && attorney.faq_items.length > 0 ? [{
        '@type': 'FAQPage',
        '@id': `${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorney.source_id}#faq`,
        mainEntity: attorney.faq_items.map(faq => ({
          '@type': 'Question',
          name: faq.question,
          acceptedAnswer: {
            '@type': 'Answer',
            text: faq.answer
          }
        }))
      }] : [])
    ]
  }
}

export function generateBreadcrumbStructuredData(breadcrumbs: Array<{name: string, href: string}>) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://findcaraccidentattorneys.org'
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: `${baseUrl}${crumb.href}`
    }))
  }
}
