// Internal linking utility for SEO optimization
// This utility automatically adds internal links to relevant keywords in blog content

interface InternalLink {
  keyword: string
  url: string
  title: string
}

// Define internal links for common legal terms and pages
const INTERNAL_LINKS: InternalLink[] = [
  {
    keyword: "car accident attorney",
    url: "/",
    title: "Find Car Accident Attorneys"
  },
  {
    keyword: "car accident lawyer",
    url: "/",
    title: "Find Car Accident Lawyers"
  },
  {
    keyword: "personal injury",
    url: "/",
    title: "Personal Injury Attorneys"
  },
  {
    keyword: "personal injury attorney",
    url: "/",
    title: "Personal Injury Attorneys"
  },
  {
    keyword: "personal injury lawyer",
    url: "/",
    title: "Personal Injury Lawyers"
  },
  {
    keyword: "legal advice",
    url: "/blog",
    title: "Legal Advice Blog"
  },
  {
    keyword: "legal representation",
    url: "/",
    title: "Legal Representation"
  },
  {
    keyword: "insurance claim",
    url: "/blog",
    title: "Insurance Claims Information"
  },
  {
    keyword: "compensation",
    url: "/",
    title: "Compensation for Car Accidents"
  },
  {
    keyword: "settlement",
    url: "/",
    title: "Car Accident Settlements"
  }
]

/**
 * Adds internal links to blog content for SEO purposes
 * @param content - The blog content to process
 * @param currentSlug - The current blog post slug to avoid self-linking
 * @returns Content with internal links added
 */
export function addInternalLinks(content: string, currentSlug?: string): string {
  let processedContent = content
  const usedLinks = new Set<string>() // Track which keywords we've already linked

  // Sort links by keyword length (longest first) to avoid partial matches
  const sortedLinks = [...INTERNAL_LINKS].sort((a, b) => b.keyword.length - a.keyword.length)

  for (const link of sortedLinks) {
    // Skip if we've already used this keyword
    if (usedLinks.has(link.keyword.toLowerCase())) {
      continue
    }

    // Create case-insensitive regex that matches whole words only
    const regex = new RegExp(`\\b(${link.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'gi')
    
    // Check if the keyword exists in the content
    const matches = processedContent.match(regex)
    if (matches && matches.length > 0) {
      // Only link the first occurrence to avoid over-optimization
      let linkAdded = false
      processedContent = processedContent.replace(regex, (match) => {
        if (!linkAdded) {
          linkAdded = true
          usedLinks.add(link.keyword.toLowerCase())
          return `<a href="${link.url}" title="${link.title}" class="text-primary hover:underline font-medium">${match}</a>`
        }
        return match
      })
    }
  }

  return processedContent
}

/**
 * Processes blog content paragraphs and adds internal links
 * @param paragraphs - Array of content paragraphs
 * @param currentSlug - Current blog post slug
 * @returns Processed paragraphs with internal links
 */
export function processContentWithLinks(paragraphs: string[], currentSlug?: string): string[] {
  const fullContent = paragraphs.join('\n')
  const processedContent = addInternalLinks(fullContent, currentSlug)
  return processedContent.split('\n')
}

/**
 * Safely renders HTML content with internal links
 * @param htmlContent - HTML content string
 * @returns JSX element with dangerously set innerHTML
 */
export function renderContentWithLinks(htmlContent: string) {
  return {
    __html: htmlContent
  }
}
