import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { generateStateSlug, generateCitySlug, generateAttorneySlug } from '@/lib/utils/seo'

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://findcaraccidentattorneys.org'
    
    // Get all attorneys
    const { data: attorneys, error } = await supabase
      .from('findcaraccidentattorneys-clean')
      .select('source_id, title, city, state, last_reviews_update')
      .eq('reviews_processed', true)
      .order('total_score', { ascending: false })

    if (error) {
      console.error('Error fetching attorneys for sitemap:', error)
      return new NextResponse('Error generating sitemap', { status: 500 })
    }

    // Get unique states and cities
    const states = [...new Set(attorneys.map(a => a.state))].sort()
    const cities = [...new Set(attorneys.map(a => `${a.city}, ${a.state}`))].sort()

    const currentDate = new Date().toISOString()

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Homepage -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <!-- Search page -->
  <url>
    <loc>${baseUrl}/search</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
`

    // Add state pages
    states.forEach(state => {
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(state)}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
`
    })

    // Add city pages
    cities.forEach(cityState => {
      const [city, state] = cityState.split(', ')
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(state)}/${generateCitySlug(city)}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
`
    })

    // Add attorney profile pages
    attorneys.forEach(attorney => {
      const lastmod = attorney.last_reviews_update || currentDate
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${generateAttorneySlug(attorney)}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
`
    })

    sitemap += `</urlset>`

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
}
