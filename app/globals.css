@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens to match professional lawyer directory design */
  --background: oklch(1 0 0); /* #ffffff - Clean white background */
  --foreground: oklch(0.278 0 0); /* #374151 - Professional dark gray text */
  --card: oklch(0.973 0 0); /* #f8fafc - Light gray card backgrounds */
  --card-foreground: oklch(0.278 0 0); /* #374151 - Dark gray text on cards */
  --popover: oklch(1 0 0); /* #ffffff - White popover background */
  --popover-foreground: oklch(0.278 0 0); /* #374151 - Dark gray popover text */
  --primary: oklch(0.278 0.118 264.052); /* #1e3a8a - Professional dark blue */
  --primary-foreground: oklch(1 0 0); /* #ffffff - White text on primary */
  --secondary: oklch(0.488 0.243 264.376); /* #4f46e5 - Indigo accent */
  --secondary-foreground: oklch(1 0 0); /* #ffffff - White text on secondary */
  --muted: oklch(0.973 0 0); /* #f8fafc - Muted light gray */
  --muted-foreground: oklch(0.439 0 0); /* #6b7280 - Muted gray text */
  --accent: oklch(0.488 0.243 264.376); /* #4f46e5 - Indigo accent */
  --accent-foreground: oklch(1 0 0); /* #ffffff - White text on accent */
  --destructive: oklch(0.577 0.245 27.325); /* #dc2626 - Error red */
  --destructive-foreground: oklch(1 0 0); /* #ffffff - White text on destructive */
  --border: oklch(0.922 0 0); /* #e5e7eb - Light border */
  --input: oklch(0.973 0 0); /* #f8fafc - Light input background */
  --ring: oklch(0.708 0 0); /* Focus ring */
  --chart-1: oklch(0.488 0.243 264.376); /* #6366f1 - Chart blue */
  --chart-2: oklch(0.769 0.188 70.08); /* #fbbf24 - Chart yellow */
  --chart-3: oklch(0.696 0.17 162.48); /* #34d399 - Chart green */
  --chart-4: oklch(0.645 0.246 16.439); /* #fb7185 - Chart pink */
  --chart-5: oklch(0.627 0.265 303.9); /* #60a5fa - Chart light blue */
  --radius: 0.5rem; /* 8px - Professional rounded corners */
  --sidebar: oklch(1 0 0); /* #ffffff - White sidebar */
  --sidebar-foreground: oklch(0.278 0 0); /* #374151 - Dark gray sidebar text */
  --sidebar-primary: oklch(0.278 0.118 264.052); /* #1e3a8a - Primary sidebar color */
  --sidebar-primary-foreground: oklch(1 0 0); /* #ffffff - White sidebar primary text */
  --sidebar-accent: oklch(0.488 0.243 264.376); /* #4f46e5 - Sidebar accent */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff - White sidebar accent text */
  --sidebar-border: oklch(0.922 0 0); /* #e5e7eb - Light sidebar border */
  --sidebar-ring: oklch(0.708 0 0); /* Sidebar focus ring */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-size: 16px; /* Increase base font size from default 14px */
  }

  /* Increase font sizes for better readability */
  h1 {
    @apply text-4xl md:text-5xl font-bold;
  }

  h2 {
    @apply text-2xl md:text-3xl font-semibold;
  }

  h3 {
    @apply text-xl md:text-2xl font-semibold;
  }

  h4 {
    @apply text-lg md:text-xl font-medium;
  }

  p {
    @apply text-base leading-relaxed;
  }

  /* Increase button text size */
  button {
    @apply text-base;
  }

  /* Increase input text size */
  input, textarea, select {
    @apply text-base;
  }

  /* Increase card text size */
  .card-content p {
    @apply text-base;
  }
}
