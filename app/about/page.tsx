import { Metada<PERSON> } from 'next'
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Scale, Users, Shield, Award, CheckCircle } from "lucide-react"

export const metadata: Metadata = {
  title: 'About Us | Find Car Accident Attorneys',
  description: 'Learn about our mission to connect car accident victims with qualified attorneys. We help you find the right legal representation for your personal injury case.',
  alternates: {
    canonical: '/about',
  },
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">About Find Car Accident Attorneys</h1>
            <p className="text-lg text-muted-foreground">
              Connecting accident victims with trusted legal representation nationwide
            </p>
          </div>

          {/* Mission Section */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-2xl font-semibold flex items-center">
                <Scale className="h-6 w-6 mr-2 text-primary" />
                Our Mission
              </h2>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                At Find Car Accident Attorneys, we understand that being involved in a car accident can be one of the most stressful and overwhelming experiences in your life. Our mission is to simplify the process of finding qualified, experienced car accident attorneys who can help you navigate the complex legal landscape and fight for the compensation you deserve.
              </p>
            </CardContent>
          </Card>

          {/* What We Do */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-2xl font-semibold flex items-center">
                <Users className="h-6 w-6 mr-2 text-primary" />
                What We Do
              </h2>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Attorney Directory</h3>
                      <p className="text-sm text-muted-foreground">Comprehensive database of verified car accident attorneys across all 50 states</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Verified Reviews</h3>
                      <p className="text-sm text-muted-foreground">Real client reviews and ratings to help you make informed decisions</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Local Expertise</h3>
                      <p className="text-sm text-muted-foreground">Find attorneys who specialize in your state's specific laws and regulations</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Free Resources</h3>
                      <p className="text-sm text-muted-foreground">Educational content about car accident law and your legal rights</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-semibold">24/7 Support</h3>
                      <p className="text-sm text-muted-foreground">Round-the-clock assistance to help you find the right attorney</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-semibold">No Cost to You</h3>
                      <p className="text-sm text-muted-foreground">Our service is completely free for accident victims</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Why Choose Us */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-2xl font-semibold flex items-center">
                <Award className="h-6 w-6 mr-2 text-primary" />
                Why Choose Us
              </h2>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <Shield className="h-12 w-12 text-primary mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Trusted Network</h3>
                  <p className="text-sm text-muted-foreground">Only verified, licensed attorneys with proven track records</p>
                </div>
                
                <div className="text-center">
                  <Users className="h-12 w-12 text-primary mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Personalized Matching</h3>
                  <p className="text-sm text-muted-foreground">We help match you with attorneys who specialize in your type of case</p>
                </div>
                
                <div className="text-center">
                  <Scale className="h-12 w-12 text-primary mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Unbiased Information</h3>
                  <p className="text-sm text-muted-foreground">Transparent reviews and ratings to help you make the best choice</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact CTA */}
          <Card>
            <CardContent className="text-center py-8">
              <h2 className="text-2xl font-semibold mb-4">Need Help Finding an Attorney?</h2>
              <p className="text-muted-foreground mb-6">
                Our team is here to help you connect with the right legal representation for your case.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/contact" 
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  Contact Us Today
                </a>
                <a 
                  href="/search" 
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                >
                  Search Attorneys
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
