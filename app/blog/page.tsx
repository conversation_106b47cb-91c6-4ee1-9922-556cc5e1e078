import { Metadata } from 'next'
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON>er } from "@/components/footer"
import { BlogSearchForm } from "@/components/blog-search-form"
import { BlogCard } from "@/components/blog-card"
import { PaginationComponent } from "@/components/pagination"
import { getBlogs, getBlogTags } from "@/lib/supabase"
import { getRecordsPerPage } from "@/lib/utils/pagination"

interface BlogPageProps {
  searchParams: Promise<{
    q?: string
    tag?: string
    page?: string
  }>
}

export const metadata: Metadata = {
  title: 'Legal Blog | Car Accident Attorney Resources & Advice',
  description: 'Expert legal advice and resources about car accidents, personal injury claims, and choosing the right attorney. Stay informed with our comprehensive legal blog.',
  alternates: {
    canonical: '/blog',
  },
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  const params = await searchParams
  const query = params.q || ''
  const tag = params.tag || ''
  const currentPage = parseInt(params.page || '1', 10)
  const recordsPerPage = getRecordsPerPage()

  // Get blog posts and tags
  const { blogs, total, error } = await getBlogs(
    currentPage,
    recordsPerPage,
    query,
    tag
  )
  const { tags } = await getBlogTags()

  const totalPages = Math.ceil(total / recordsPerPage)
  const hasResults = blogs.length > 0
  const hasFilters = query || tag

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-5xl font-bold mb-4">
            {hasFilters ? 'Blog Search Results' : 'Legal Blog & Resources'}
          </h1>
          <p className="text-xl text-muted-foreground">
            {hasFilters
              ? `Found ${total} articles matching your search criteria`
              : 'Expert legal advice, tips, and resources for car accident victims and personal injury claims'
            }
          </p>
        </div>

        {/* Search Form */}
        <div className="mb-8">
          <BlogSearchForm
            initialQuery={query}
            initialTag={tag}
            tags={tags}
          />
        </div>

        {error && (
          <div className="mb-8 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-destructive">Error loading blog posts: {error}</p>
          </div>
        )}

        {hasFilters && (
          <div className="mb-6">
            <div className="flex flex-wrap items-center gap-2 text-sm">
              <span className="text-muted-foreground">Active filters:</span>
              {query && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded">
                  Search: "{query}"
                </span>
              )}
              {tag && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded">
                  Tag: {tag.replace('-', ' ')}
                </span>
              )}
            </div>
          </div>
        )}

        {hasResults ? (
          <>
            {/* Results summary */}
            <div className="mb-6">
              <p className="text-muted-foreground">
                Showing {(currentPage - 1) * recordsPerPage + 1}-{Math.min(currentPage * recordsPerPage, total)} of {total} articles
              </p>
            </div>

            {/* Results grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {blogs.map((blog) => (
                <BlogCard key={blog.id} blog={blog} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <PaginationComponent
                currentPage={currentPage}
                totalPages={totalPages}
                baseUrl="/blog"
              />
            )}
          </>
        ) : hasFilters ? (
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-4">No articles found</h2>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search criteria or browse all articles.
            </p>
            <BlogSearchForm
              initialQuery=""
              initialTag=""
              tags={tags}
              showClearButton={false}
            />
          </div>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-4">Start Your Search</h2>
            <p className="text-muted-foreground">
              Use the search form above to find specific legal articles and resources.
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
}
