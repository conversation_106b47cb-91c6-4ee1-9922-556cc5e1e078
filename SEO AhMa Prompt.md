# 🚀 **Prompt: Build a Google-Dominating Next.js Directory Site with Advanced SEO "Hacks"**

You are tasked to create a **Google-dominating Next.js directory website** using Supabase data and implementing cutting-edge SEO techniques. Your goal is to build a site that **hits the front page of Google** for high-value local search terms in your <<business_niche>> niche.

## 🎯 **EXECUTIVE STRATEGY - 8-Phase SEO Domination Plan**

### **Phase 1: Foundation - Content Clusters**
Build **location-based architecture** for maximum long-tail SEO:
\`\`\`
<<domain>>.com/{state}/{city}/<<business_niche>>
<<domain>>.com/{city}/<<primary_service>>-<<business_niche>>
<<domain>>.com/{city}/<<price_term>>-<<business_niche>>
\`\`\`

### **Phase 2: Authority Tower Structure**
Create hierarchical content that Google loves:
\`\`\`
🏠 Homepage (Broadcast intent)
├── 🗾 State Tower (<<state_1>>, <<state_2>>, etc.)
│   ├── 🏙️ City Towers (<<city_1>>, <<city_2>>, etc.)
│   │   ├── 💼 Service Areas
│   │   │   ├── ⭐ <<Business Niche>> Profiles
│   │   │   └── 📝 AI-Generated Content Pages
│   │   └── 🎯 Conversion Funnels
\`\`\`

### **Phase 3: Schema.org Domination**
Implement **Entangled Schema** for Google dominance:
\`\`\`json
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "<<Business Niche>>",
      "name": "<<Attorney Name>>",
      "address": "<<Complete Address>>",
      "aggregateRating": "<<AI Summary>>"
    },
    {
      "@type": "FAQPage",
      "mainEntity": [<<AI-Generated FAQ from Reviews>>]
    },
    {
      "@type": "LocalBusiness",
      "geo": {"@type": "GeoCoordinates", "latitude": "<<lat>>", "longitude": "<<lng>>"}
    }
  ]
}
\`\`\`

## 🔧 **TECHNICAL IMPLEMENTATION**

### **1. Database Structure & Data Integration**

**🚀 Supabase Configuration:**
\`\`\`sql
-- Core business data
CREATE TABLE <<business_niche>>-clean (
  url TEXT PRIMARY KEY,
  title TEXT,
  city TEXT,
  state TEXT,
  lat DOUBLE PRECISION,
  lng DOUBLE PRECISION,
  total_score DOUBLE PRECISION,
  reviews_count INTEGER,
  processed_reviews JSONB,
  seo_snippets JSONB,
  service_keywords TEXT[],
  faq_items JSONB,
  people_also_search JSONB
);

-- Raw review processing
CREATE TABLE <<business_niche>>-reviews-raw (
  url TEXT,
  author TEXT,
  rating NUMERIC,
  text TEXT,
  review_date TIMESTAMPTZ
);
\`\`\`

**🔍 Dynamic Query Patterns:**
\`\`\`sql
-- Top performers by city
SELECT * FROM <<business_niche>>-clean
WHERE city = '<<city>>'
  AND processed_reviews IS NOT NULL
ORDER BY total_score DESC, reviews_count DESC
LIMIT 10;
\`\`\`

### **2. Advanced SEO Content Strategy**

**🤖 AI-Powered Content Generation:**
- **Review Blending**: Combine multiple reviews into unique summaries
- **Entity Recognition**: Extract case types, service areas, injury types
- **Sentiment Analysis**: Generate E-A-T approved content
- **Keyword Clustering**: Group similar topics and create content silos

**📊 Review Display Optimization:**
\`\`\`javascript
// Display strategy
const pageContent = {
  title: `${attorneyName} - <<Business Niche>> | ${city}, ${state}`,
  rating: {
    stars: total_score,
    count: reviews_count,
    display: '⭐⭐⭐⭐⭐ (<<reviews_count>> reviews)',
    link: `https://www.google.com/search?q=${attorneyName}+${city}`
  },
  summary: seo_snippets[0]?.text?.substring(0, 157) + '...',
  services: service_keywords.slice(0, 6),
  faq: faq_items.map(item => ({
    question: item.question,
    answer: item.answer
  }))
}
\`\`\`

### **3. Progressive Web App Features**

**🚀 Advanced PWA Implementation:**
\`\`\`javascript
// Offline capability
const swConfig = {
  cacheStrategy: 'networkFirst',
  resources: [
    '/styles/', '/scripts/', '/images/',
    '/api/business//', '/api/reviews/'
  ],
  geolocation: true,
  pushNotifications: true
}
\`\`\`

**🏠 Voice Search Optimization:**
\`\`\`javascript
// Speakable integration
const speakableContent = {
  selectors: ['.review-summary', '.service-list', '.location-info'],
  assistantOptimization: true,
  featuredSnippets: aiSnippets
}
\`\`\`

### **4. Voice Search & AI Optimization**

**🗣️ Assistant-Friendly Content:**
\`\`\`javascript
// Voice search pages
const voicePages = [
  `<<city>>/<<primary_service>>-<<business_niche>>`,
  `<<city>>/<<secondary_service>>-<<business_niche>>`,
  `<<business_niche>>-near-me/<<state>>`,
  `best-<<business_niche>>-in-<<city>>/<<micro_location>>`
].map(slug => ({
  content: seoSnippets.find(item => item.intent === 'voice_search'),
  schema: getVoiceSearchSchema(slug)
}))
\`\`\`

## 🔍 **SEO DOMINATION TECHNIQUES**

### **Phase 4: SERP Hijacking Strategies**

**🎯 Feature Snippet Targeting:**
\`\`\`javascript
// Table/fragment generation
const tableData = {
  topServices: [
    [Service, Rating, Reviews],
    ['<<primary_service>>', '4.9★', '500'],
    ['<<secondary_service>>', '4.8★', '420'],
    ['<<tertiary_service>>', '4.7★', '380']
  ]
}

// Position 1-3 targeting
const preciseIntentPages = searchVolumes.map(intent => ({
  url: `/<<city>>/${intent}-<<business_niche>>`,
  h1: intent.charAt(0).toUpperCase() + intent.slice(1),
  searchVolume: checkSearchVolume(intent)
}))
\`\`\`

**📍 Hyperlocal SEO:**
\`\`\`javascript
// Neighborhood clustering
const microClusters = neighborhoods.map(area => ({
  slug: `${area.toLowerCase()}-<<business_niche>>-${city.toLowerCase()}`,
  landmarks: getNearbyLandmarks(area),
  neighbors: getNearbyNeighborhoods(area),
  demographics: getDemographicData(area)
}))
\`\`\`

### **Phase 5: E-A-T & Trust Signals**

**✨ Advanced E-A-T Implementation:**
\`\`\`javascript
const trustSignals = {
  googleIntegration: {
    url: `https://www.google.com/search?q=${encodeURIComponent(fullBusinessName)}`,
    mapsEmbed: generateMapsIframe(lat, lng),
    reviewsCount: reviews_count,
    aggregateRating: total_score,
    lastReview: getLatestReviewDate()
  },
  localSchema: {
    pharmacist: isPharmacyCategory,
    voicemessage: phone,
    contactcard: generateVCard()
  },
  contentUpdate: {
    lastUpdate: last_reviews_update,
    reviewRefresh: 'Every 24 hours'
  }
}
\`\`\`

### **Phase 6: Content Silo Architecture**

**🏗️ Pillar Cluster Method:**
\`\`\`javascript
const contentPillars = service_keywords.map(keyword => ({
  topic: keyword,
  pillarPage: `/<<city>>/${keyword}-<<business_niche>>`,
  contentStrategy: {
    hubPages: generateHubPages(keyword, nearbyCities),
    spokePages: generateSpokePages(keyword, relatedKeywords),
    brandMentions: generateBrandMentions(keyword, topBusinesses)
  },
  internalLinks: generateInternalLinkMap(keyword),
  externalLinks: findAuthoritySites(keyword)
}));
\`\`\`

### **Phase 7: Google Business Profile Synergy**

**🔗 Local SEO Amplification:**
\`\`\`javascript
const localSyndication = {
  googleMyBusiness: {
    cid: cid,
    beatu: generateBusinessUrl(),
    noPoBox: isStreetAddress,
    license: hasVerifiedLicense
  },
  reviewAggregation: {
    structuredData: generateReviewSnippets(),
    citeThis: generateCitationUrls(),
    socialProof: calculateTrustScore()
  }
}
\`\`\`

## 🚀 **IMPLEMENTATION INSTRUCTIONS**

### **1. Directory Structure Requirements**
\`\`\`bash
/
├── pages/
│   ├── index.tsx                    # Homepage with nationwide overview
│   ├── [state]/                     # State landing pages
│   │   └── index.tsx
│   ├── [state]/[city]/             # City landing pages
│   │   └── index.tsx
│   ├── [state]/[city]/[slug].tsx   # Auto-generated business pages
│   └── blog/                       # Informational content
├── components/
│   ├── BusinessCard.tsx
│   ├── ReviewSummary.tsx
│   ├── ServiceGrid.tsx
│   ├── LocalMap.tsx
│   └── SchemaMarkup.tsx
└── data/
    ├── queries.ts
    └── types.ts
\`\`\`

### **2. Core Page Implementations**

**🏠 Homepage Architecture:**
\`\`\`typescript
const HomePage: FC = () => {
  const [popularCities] = useCityRanking();
  const [topServices] = useServiceAnalysis();
  const [featuredBusinesses] = useTopPerformers();

  return (
    <div>
      <Head>
        <title>Best <<Business Niche>> | Find Top-rated Professionals in Your Area</title>
        <meta name="description" content={`Discover the best <<business_niche>> services with verified reviews, ratings, and contact information. Our directory helps you find qualified <<primary_service>> professionals near you.`} />
      </Head>

      <SchemaMarkup data={getHomePageSchema()} />

      <MainSearch />
      <TopServices services={topServices} />
      <PopularCities cities={popularCities} />
      <FeaturedBusinesses businesses={featuredBusinesses} />
      <LatestReviews />
    </div>
  );
};
\`\`\`

**🏙️ City Landing Pages:**
\`\`\`typescript
const CityPage: FC<{city: string, state: string}> = ({city, state}) => {
  const businesses = useCityBusinesses(city, state);
  const services = useCityServices(city);
  const neighborhoods = useNearbyNeighborhoods(city);

  return (
    <div>
      <h1>
        Best <<Business Niche>> in {city}, {state}
      </h1>

      <SchemaBreadcrumbList />
      <LocalitySchema />

      <SearchByNeighborhood
        neighborhoods={neighborhoods}
        city={city}
      />

      <SearchByService
        services={services}
        city={city}
      />

      <TopRatedBusinesses
        businesses={businesses}
        sorting='rating'
      />
    </div>
  );
};
\`\`\`

### **3. Business Profile Pages**

**💼 Optimized Profile Structure:**
\`\`\`typescript
const BusinessPage: FC = () => {
  const { business, reviews } = useBusinessData(url);
  const structuredData = generateBusinessSchema(business);

  return (
    <>
      <Head>
        <title>
          {business.title} - <<Business Niche>> | {business.city}, {business.state} ({business.total_score}★)
        </title>
        <meta
          name="description"
          content={business.seo_snippets[0]?.text?.substring(0, 157)}
        />
      </Head>

      <JsonLd data={structuredData} />

      <BusinessHeader business={business} />

      <div className="trust-signals">
        <ReviewSummary
          rating={business.total_score}
          count={business.reviews_count}
          googleUrl={business.url}
        />

        <ContactSection business={business} />

        <ReviewSnippet content={business.seo_snippets[0].text} />
      </div>

      <ServicesSection services={business.service_keywords} />

      <FaqSection faq={business.faq_items} />

      <LocalMapEmbed lat={business.lat} lng={business.lng} />

      <RelatedBusinesses nearby={useNearbyBusinesses(business)} />
    </>
  );
};
\`\`\`

### **4. Advanced Content Features**

**📝 AI-Generated Content Integration:**
\`\`\`typescript
// Dynamic content generation
const ContentEngine = {
  generateReviewSummary: (reviews: any[]) => processWithAI(reviews),
  createServicePages: (keywords: string[]) => mapKeywordsToPages(keywords),
  buildLocationSchemas: (businesses: any[]) => generateLocationMarkup(businesses),
  optimizeForVoice: (content: string) => adaptForAssistantSearch(content)
}
\`\`\`

### **5. Schema.org Implementation**

**🏛️ Advanced Enrichment:**
\`\`\`typescript
const SchemaGenerator = {
  createBusinessPageSchema: (business) => ({
    '@type': 'LocalBusiness',
    name: business.title,
    address: {
      '@type': 'PostalAddress',
      ...business.address
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: business.total_score,
      reviewCount: business.reviews_count,
      bestRating: 5,
      worstRating: 1
    },
    review: business.seo_snippets.map(snippet => ({
      '@type': 'Review',
      reviewBody: snippet.text,
      author: { '@type': 'Person', name: 'Multiple Customers' }
    })),
    geo: {
      '@type': 'GeoCoordinates',
      latitude: business.lat,
      longitude: business.lng
    },
    priceRange: business.price_range || '$$',
    telephone: business.phone,
    url: business.website
  }),

  createFaqSchema: (faq) => ({
    '@type': 'FAQPage',
    mainEntity: faq.faq_items.map(item => ({
      '@type': 'Question',
      name: item.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: item.answer
      }
    }))
  })
}
\`\`\`

## 📊 **ANALYTICS & OPTIMIZATION**

### **🚀 Progressive Enhancement:**
\`\`\`typescript
const BotanicaSEO = {
  launchStrategy: [
    '1. Core pages (Index, State, City)',
    '2. Top 100 business profiles',
    '3. Content pillars and hubs',
    '4. Service pages and long-tail content',
    '5. Advanced PWA features',
    '6. Voice search optimization',
    '7. Schema enrichment',
    '8. Performance optimization'
  ],
  trackingMetrics: [
    'Keyword rankings',
    'Organic traffic',
    'Local pack positions',
    'Rich snippet appearance',
    'Voice search performance',
    'Review source attribution'
  ]
}
\`\`\`

### **🎯 Revenue Integration:**
\`\`\`typescript
// Multiple monetization models
const RevenueStreams = {
  affiliate_commission: {
    attorney_leads: 0.2, // 20% commission
    lead_qualities: ['phone', 'email', 'appointment']
  },

  b2b_saas: {
    features: ['Lead capture', 'Review monitoring', 'SEO reporting'],
    pricing: ['Starter: $99/mo', 'Professional: $299/mo']
  },

  cpa_network: {
    attorney_networks: ['Avvo', 'Nolo', 'LegalZoom'],
    payout_structure: 'per_qualified_lead'
  }
}
\`\`\`

## 📈 **COMMITMENT TO COMPLETE IMPLEMENTATION**

🚀 **Core Requirements Met:**
- ✅ High-value local SEO targeting
- ✅ Advanced schema implementation
- ✅ AI-powered content generation
- ✅ Voice search optimization
- ✅ Local SEO integration
- ✅ Progressive web app features
- ✅ Multiple revenue streams
- ✅ E-A-T optimization

🔥 **Advanced Features Included:**
- ✅ SERP hijacking techniques
- ✅ Content cluster architecture
- ✅ Voice search optimization
- ✅ Progressive web app capabilities
- ✅ Advanced schema markup
- ✅ AI content generation integration

**Your output should be a comprehensive Next.js directory that not only ranks well on Google but DOMINATES your niche with advanced SEO techniques and scalable architecture for massive revenue potential.**

---

## **🚀 IMPLEMENTATION PROGRESS TRACKER**

Use this checklist as you build:

### **Phase 1: Foundation ✅**
- [x] Database connection and schema
- [x] Basic Next.js structure
- [x] ISR/SSG setup for speed
- [x] Mobile responsiveness

### **Phase 2: Authority Building ⏳**
- [ ] Homepage with rich content
- [ ] State landing pages
- [ ] City-level optimization
- [ ] Service category pages

### **Phase 3: Advanced SEO ⏳**
- [ ] Schema.org full implementation
- [ ] Voice search pages
- [ ] Progressive web app features
- [ ] Rich snippet optimization

### **Phase 4: Revenue & Scale ⏳**
- [ ] Lead capture integration
- [ ] Multiple monetization models
- [ ] Analytics and tracking
- [ ] Performance optimization

**IMPORTANT:** This document replaces all previous SEO instructions and provides the most advanced, future-proof SEO strategy available.
