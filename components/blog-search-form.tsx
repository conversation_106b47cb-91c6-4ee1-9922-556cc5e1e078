'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, X } from "lucide-react"

interface BlogSearchFormProps {
  initialQuery?: string
  initialTag?: string
  tags: string[]
  showClearButton?: boolean
}

export function BlogSearchForm({ 
  initialQuery = '', 
  initialTag = '', 
  tags,
  showClearButton = true 
}: BlogSearchFormProps) {
  const [query, setQuery] = useState(initialQuery)
  const [tag, setTag] = useState(initialTag)
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const params = new URLSearchParams()
    if (query.trim()) params.set('q', query.trim())
    if (tag.trim() && tag !== 'all') params.set('tag', tag.trim())

    const searchUrl = `/blog${params.toString() ? `?${params.toString()}` : ''}`
    router.push(searchUrl)
  }

  const handleClear = () => {
    setQuery('')
    setTag('all')
    router.push('/blog')
  }

  const formatTagName = (tagName: string) => {
    return tagName.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search Query */}
        <div className="md:col-span-2">
          <Input
            type="text"
            placeholder="Search blog posts by title or content..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="w-full"
          />
        </div>

        {/* Tag Filter */}
        <div>
          <Select value={tag} onValueChange={setTag}>
            <SelectTrigger>
              <SelectValue placeholder="Select Tag" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tags</SelectItem>
              {tags.map((tagName) => (
                <SelectItem key={tagName} value={tagName}>
                  {formatTagName(tagName)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex gap-2">
        <Button type="submit" className="flex items-center">
          <Search className="h-4 w-4 mr-2" />
          Search
        </Button>
        
        {showClearButton && (query || tag) && (
          <Button type="button" variant="outline" onClick={handleClear} className="flex items-center">
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
        )}
      </div>
    </form>
  )
}
