'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, X } from "lucide-react"

interface SearchFormProps {
  initialQuery?: string
  initialCity?: string
  initialState?: string
  states: string[]
  showClearButton?: boolean
}

export function SearchForm({ 
  initialQuery = '', 
  initialCity = '', 
  initialState = '', 
  states,
  showClearButton = true 
}: SearchFormProps) {
  const [query, setQuery] = useState(initialQuery)
  const [city, setCity] = useState(initialCity)
  const [state, setState] = useState(initialState)
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const params = new URLSearchParams()
    if (query.trim()) params.set('q', query.trim())
    if (city.trim()) params.set('city', city.trim())
    if (state.trim() && state !== 'all') params.set('state', state.trim())

    const searchUrl = `/search${params.toString() ? `?${params.toString()}` : ''}`
    router.push(searchUrl)
  }

  const handleClear = () => {
    setQuery('')
    setCity('')
    setState('all')
    router.push('/search')
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search Query */}
        <div className="md:col-span-2">
          <Input
            type="text"
            placeholder="Search by attorney name, firm, or specialty..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="w-full"
          />
        </div>

        {/* City */}
        <div>
          <Input
            type="text"
            placeholder="City"
            value={city}
            onChange={(e) => setCity(e.target.value)}
            className="w-full"
          />
        </div>

        {/* State */}
        <div>
          <Select value={state} onValueChange={setState}>
            <SelectTrigger>
              <SelectValue placeholder="Select State" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All States</SelectItem>
              {states.map((stateName) => (
                <SelectItem key={stateName} value={stateName}>
                  {stateName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex gap-2">
        <Button type="submit" className="flex items-center">
          <Search className="h-4 w-4 mr-2" />
          Search
        </Button>
        
        {showClearButton && (query || city || state) && (
          <Button type="button" variant="outline" onClick={handleClear} className="flex items-center">
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
        )}
      </div>
    </form>
  )
}
