import { LawyerCard } from "@/components/lawyer-card"
import { BlogCard } from "@/components/blog-card"
import { Attorney, Blog } from "@/lib/supabase"

interface RelatedItemsProps {
  title: string
  items: Attorney[] | Blog[]
  type: 'attorneys' | 'blogs'
}

export function RelatedItems({ title, items, type }: RelatedItemsProps) {
  if (items.length === 0) {
    return null
  }

  return (
    <section className="mt-12">
      <h2 className="text-2xl font-semibold mb-6">{title}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {type === 'attorneys' 
          ? (items as Attorney[]).map((attorney) => (
              <LawyerCard key={attorney.source_id} lawyer={attorney} />
            ))
          : (items as Blog[]).map((blog) => (
              <BlogCard key={blog.id} blog={blog} />
            ))
        }
      </div>
    </section>
  )
}
